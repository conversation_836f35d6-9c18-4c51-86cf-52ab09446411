'use client';

import React from 'react';
import { Video, Cloud, Home, Phone, CheckCircle } from 'lucide-react';
import { scrollToElement } from '@/lib/utils/scroll';

const processSteps = [
  {
    icon: Phone,
    emoji: '📞',
    title: 'Konsultacja online',
    description: 'Rozmowa przez telefon lub wideorozmowę o Twoich potrzebach i wizji projektu',
    color: 'from-blue-500 to-blue-600',
  },
  {
    icon: Cloud,
    emoji: '☁️',
    title: 'Projekt w chmurze',
    description: 'Tworzenie projektu z dostępem online - możesz śledzić postępy w czasie rzeczywistym',
    color: 'from-green-500 to-green-600',
  },
  {
    icon: Video,
    emoji: '🎥',
    title: 'Prezentacja online',
    description: 'Prezentacja gotowego projektu przez wideorozmowę z możliwością wprowadzenia poprawek',
    color: 'from-purple-500 to-purple-600',
  },
  {
    icon: Home,
    emoji: '🏠',
    title: 'Wdrożenie bez wychodzenia z domu',
    description: 'Uruchomienie strony i przekazanie dostępów - wszystko zdalnie',
    color: 'from-orange-500 to-orange-600',
  },
];

export default function Process() {
  return (
    <section id="process" className="py-2 sm:py-3 lg:py-4 theme-bg-primary ipad-mini-section-spacing">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold theme-text-primary mb-4">
            Cały proces online
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto mb-6"></div>
          <p className="text-lg theme-text-muted max-w-3xl mx-auto">
            Konsultacja przez telefon/wideorozmowę, projekt w chmurze, wdrożenie bez wychodzenia z domu
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {processSteps.map((step, index) => {
            const Icon = step.icon;
            
            return (
              <div
                key={index}
                className="relative group"
              >
                {/* Step Number Emoji */}
                <div className="absolute -top-4 -left-4 z-10 text-3xl drop-shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300">
                  {['1️⃣', '2️⃣', '3️⃣', '4️⃣'][index]}
                </div>

                {/* Card */}
                <div className="theme-bg-primary rounded-2xl shadow-lg p-6 h-full theme-border hover:shadow-2xl transition-all duration-300 group-hover:scale-105">
                  {/* Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon size={32} className="text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold theme-text-primary mb-3">
                    {step.title}
                  </h3>
                  <p className="theme-text-muted leading-relaxed">
                    {step.description}
                  </p>
                </div>

                {/* Connector Line (except for last item) */}
                {index < processSteps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-primary-300 to-secondary-300 transform -translate-y-1/2 z-0"></div>
                )}
              </div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="theme-bg-secondary rounded-2xl p-8">
            <div className="flex items-center justify-center mb-4">
              <CheckCircle className="text-green-500 mr-2" size={24} />
              <span className="text-lg font-semibold theme-text-primary">
                100% zdalnie, 100% profesjonalnie
              </span>
            </div>
            <p className="theme-text-muted mb-6">
              Nie musisz wychodzić z domu, żeby mieć profesjonalną stronę internetową
            </p>
            <button
              onClick={() => {
                // NOTE: These offset values are optimized and should NOT be changed unless specifically requested
                scrollToElement('#contact', {
                  behavior: 'smooth',
                  mobileOffset: 16, // Optimized value - do not change without explicit request
                  desktopOffset: 55 // Optimized value - do not change without explicit request
                });
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              Rozpocznij współpracę online
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
