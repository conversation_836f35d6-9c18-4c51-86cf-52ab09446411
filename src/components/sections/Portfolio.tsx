'use client';

import { useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { EmblaCarouselType } from 'embla-carousel';
import Autoplay from 'embla-carousel-autoplay';
import { Globe, ShoppingCart, Zap, ArrowRight, ChevronLeft, ChevronRight, Users } from 'lucide-react';
import { scrollToElement } from '@/lib/utils/scroll';

const mockups = [
  {
    icon: Globe,
    title: 'Strona wizytówkowa',
    description: 'Nowoczesna strona dla firmy usługowej',
    features: ['Responsywny design', 'SEO', 'Formularz kontaktowy'],
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
  },
  {
    icon: ShoppingCart,
    title: 'Landing page',
    description: 'Strona sprzedażowa z formularzem kontaktowym',
    features: ['Formularz leadów', 'Analityka', 'Optymalizacja konwersji'],
    color: 'from-green-500 to-green-600',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
  },
  {
    icon: Zap,
    title: 'Modernizacja',
    description: 'Odświeżenie przestarzałej strony',
    features: ['Nowy design', 'Optymalizacja', 'Mobile-first'],
    color: 'from-purple-500 to-purple-600',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
  },
  {
    icon: Users,
    title: 'Konsultacje IT - Portfolio',
    description: 'Projekty doradztwa technologicznego',
    features: ['Audyt bezpieczeństwa', 'Strategia digitalna', 'Optymalizacja SEO'],
    color: 'from-orange-500 to-orange-600',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
  },
];

export default function Portfolio() {
  // Carousel state
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  // Embla Carousel setup with responsive breakpoints
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'start',
      slidesToScroll: 1,
      breakpoints: {
        '(min-width: 768px)': { slidesToScroll: 1 },
        '(min-width: 1024px)': { slidesToScroll: 1 }
      }
    },
    [Autoplay({ delay: 4000, stopOnInteraction: false })]
  );

  // Navigation functions
  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const scrollTo = useCallback((index: number) => {
    if (emblaApi) emblaApi.scrollTo(index);
  }, [emblaApi]);

  const onInit = useCallback((emblaApi: EmblaCarouselType) => {
    setScrollSnaps(emblaApi.scrollSnapList());
  }, []);

  const onSelect = useCallback((emblaApi: EmblaCarouselType) => {
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, []);

  useEffect(() => {
    if (!emblaApi) return;

    onInit(emblaApi);
    onSelect(emblaApi);
    emblaApi.on('reInit', onInit);
    emblaApi.on('select', onSelect);
  }, [emblaApi, onInit, onSelect]);



  const scrollToContact = () => {
    // Use the scroll utility for consistent mobile offset behavior
    // NOTE: These offset values are optimized and should NOT be changed unless specifically requested
    scrollToElement('#contact', {
      behavior: 'smooth',
      mobileOffset: 16, // Optimized value - do not change without explicit request
      desktopOffset: 55 // Optimized value - do not change without explicit request
    });
  };

  return (
    <section id="portfolio" className="py-2 sm:py-3 lg:py-4 theme-bg-primary ipad-mini-section-spacing">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="theme-bg-secondary rounded-2xl p-8">
        <div className="text-center mb-4">
          <h2 className="text-3xl sm:text-4xl font-bold theme-text-primary mb-4">
            Przykłady realizacji
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto mb-4"></div>
        </div>

        {/* Info Box */}
        <div className="theme-bg-card rounded-2xl p-8 mb-4 text-center">
          <h3 className="text-2xl font-bold theme-text-primary mb-2">
            Portfolio w przygotowaniu
          </h3>
          <p className="text-lg theme-text-muted max-w-3xl mx-auto mb-6">
            Rozpoczynam działalność z wysokimi standardami jakości i oferuję
            <strong> specjalne warunki dla pierwszych klientów</strong>.
            Zobacz, jak będą wyglądać przyszłe realizacje!
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            <span className="px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              Specjalne ceny
            </span>
            <span className="px-4 py-2 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
              Priorytetowe traktowanie
            </span>
            <span className="px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
              Dodatkowe wsparcie
            </span>
          </div>
        </div>

        {/* Embla Carousel */}
        <div className="relative mb-16 pb-8 mt-8">
          <div className="embla overflow-hidden rounded-xl" ref={emblaRef}>
            <div className="embla__container flex items-stretch mb-12">
              {mockups.map((mockup, index) => {
                const Icon = mockup.icon;
                return (
                  <div
                    key={index}
                    className="embla__slide flex-[0_0_100%] md:flex-[0_0_50%] lg:flex-[0_0_33.333%] px-4"
                  >
                    <div className="group theme-bg-card rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden hover:-translate-y-1 h-full flex flex-col">
                      {/* Mockup Display Area */}
                      <div className={`h-48 ${mockup.bgColor} relative overflow-hidden`}>
                        {/* Browser Mockup */}
                        <div className="absolute top-4 left-4 right-4 bg-white rounded-lg shadow-sm">
                          <div className="flex items-center px-3 py-2 border-b border-gray-200">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                            </div>
                            <div className="flex-1 mx-4 h-4 bg-gray-300 dark:bg-gray-100 rounded"></div>
                          </div>
                          <div className="p-3 space-y-2">
                            <div className="h-3 bg-gray-400 dark:bg-gray-200 rounded w-3/4"></div>
                            <div className="h-2 bg-gray-300 dark:bg-gray-100 rounded w-full"></div>
                            <div className="h-2 bg-gray-300 dark:bg-gray-100 rounded w-2/3"></div>
                          </div>
                        </div>

                        {/* Icon */}
                        <div className={`absolute bottom-4 right-4 w-12 h-12 bg-gradient-to-r ${mockup.color} rounded-xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                          <Icon size={24} />
                        </div>
                      </div>

                      {/* Content */}
                      <div className="p-6 flex-1 flex flex-col">
                        <h3 className="text-xl font-bold theme-text-primary mb-2">
                          {mockup.title}
                        </h3>
                        <p className="theme-text-muted mb-4">
                          {mockup.description}
                        </p>

                        {/* Features */}
                        <ul className="space-y-1 mb-4 flex-1">
                          {mockup.features.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-center text-sm theme-text-muted">
                              <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>

                        <div className="text-sm theme-text-muted italic mt-auto">
                          Przykład realizacji
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-center space-x-4 mt-6">
            <button
              onClick={scrollPrev}
              className="p-3 rounded-full bg-white dark:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-gray-200 dark:border-gray-600"
              aria-label="Poprzedni slajd"
            >
              <ChevronLeft size={20} className="text-gray-600 dark:text-gray-300" />
            </button>

            {/* Dynamic Navigation Dots */}
            <div className="flex items-center justify-center space-x-3 px-4">
              {scrollSnaps.map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollTo(index)}
                  className={`relative transition-all duration-300 ease-in-out transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full ${
                    index === selectedIndex
                      ? 'w-4 h-4 p-1'
                      : 'w-2.5 h-2.5 hover:w-3 hover:h-3'
                  }`}
                  aria-label={`Przejdź do slajdu ${index + 1}`}
                  aria-current={index === selectedIndex ? 'true' : 'false'}
                >
                  <div
                    className={`w-full h-full rounded-full transition-all duration-300 ease-in-out ${
                      index === selectedIndex
                        ? 'bg-primary-600 dark:bg-primary-500 shadow-xl ring-2 ring-primary-200 dark:ring-primary-800 ring-offset-1'
                        : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 hover:shadow-md'
                    }`}
                  />
                  {/* Enhanced active dot glow effect */}
                  {index === selectedIndex && (
                    <>
                      <div className="absolute inset-0 rounded-full bg-primary-500 opacity-40 animate-pulse" />
                      <div className="absolute inset-0 rounded-full bg-primary-400 opacity-20 scale-150 animate-pulse" style={{ animationDelay: '0.5s' }} />
                    </>
                  )}
                </button>
              ))}
            </div>

            <button
              onClick={scrollNext}
              className="p-3 rounded-full bg-white dark:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-gray-200 dark:border-gray-600"
              aria-label="Następny slajd"
            >
              <ChevronRight size={20} className="text-gray-600 dark:text-gray-300" />
            </button>
          </div>
        </div>

        {/* CTA Section - Enhanced Mobile Centering */}
        <div className="text-center theme-bg-card rounded-2xl shadow-lg p-6 sm:p-8">
          <h3 className="text-xl sm:text-2xl font-bold theme-text-primary mb-4">
            Zostań moim klientem
          </h3>
          <p className="theme-text-muted mb-6 sm:mb-8 max-w-2xl mx-auto px-4 sm:px-0">
            Skorzystaj z wyjątkowej okazji i otrzymaj profesjonalną stronę internetową
            w specjalnej cenie. Moi klienci zawsze otrzymują dodatkowe korzyści i priorytetowe wsparcie.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4 sm:px-0">
            <button
              onClick={scrollToContact}
              className="w-full sm:w-auto bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center"
            >
              Skorzystaj z oferty
              <ArrowRight size={20} className="ml-2" />
            </button>

            <div className="text-sm text-gray-500 dark:text-gray-400 text-center">
              Bezpłatna konsultacja • Bez zobowiązań
            </div>
          </div>

          {/* Benefits - Enhanced Mobile Centering */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-4 mt-8 pt-8 border-t theme-border">
            <div className="flex flex-col items-center justify-center text-center px-4">
              <div className="text-2xl sm:text-3xl font-bold theme-text-blue mb-2">-30%</div>
              <div className="text-sm theme-text-muted">Specjalna cena</div>
            </div>
            <div className="flex flex-col items-center justify-center text-center px-4">
              <div className="text-2xl sm:text-3xl font-bold theme-text-purple mb-2">+60</div>
              <div className="text-sm theme-text-muted">Dni wsparcia</div>
            </div>
            <div className="flex flex-col items-center justify-center text-center px-4">
              <div className="text-2xl sm:text-3xl font-bold theme-text-green mb-2">1:1</div>
              <div className="text-sm theme-text-muted">Dedykowane wsparcie</div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
}
