'use client';

import { useState, useEffect } from 'react';
import { Tooltip } from 'react-tooltip';
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';

// Google Translate cookie name (predefined by Google)
const COOKIE_NAME = 'googtrans';

// Language configuration with German added
const languages = [
  { code: 'pl', flag: '🇵🇱', name: '<PERSON><PERSON>', tooltip: 'Przełącz na polski' },
  { code: 'en', flag: '🇬🇧', name: 'English', tooltip: 'Switch to English' },
  { code: 'de', flag: '🇩🇪', name: '<PERSON><PERSON><PERSON>', tooltip: '<PERSON><PERSON> Deutsch wechseln' },
  { code: 'uk', flag: '🇺🇦', name: 'Українська', tooltip: 'Перемкнути на українську' },
];

// Language descriptor interface
interface LanguageDescriptor {
  code: string;
  flag: string;
  name: string;
  tooltip: string;
}

// Global configuration for Google Translate
declare global {
  interface Window {
    __GOOGLE_TRANSLATION_CONFIG__: {
      languages: LanguageDescriptor[];
      defaultLanguage: string;
    };
    google: {
      translate: {
        TranslateElement: new (config: {
          pageLanguage: string;
          includedLanguages: string;
          layout: number;
          autoDisplay: boolean;
        }) => void;
      };
    };
    googleTranslateElementInit: () => void;
  }
}

// Helper functions for cookie management
const getCookie = (name: string): string | null => {
  if (typeof document === 'undefined') return null;
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
};

// Detect current language from multiple sources
const detectCurrentLanguage = (): string => {
  if (typeof window === 'undefined') return 'pl';

  // Method 1: Check Google Translate cookie
  const cookieValue = getCookie(COOKIE_NAME);
  if (cookieValue) {
    // Parse cookie format: /auto/en, /pl/en, etc.
    const parts = cookieValue.split('/');
    if (parts.length >= 3) {
      const targetLang = parts[2];
      // Validate that it's one of our supported languages
      if (targetLang && languages.some(lang => lang.code === targetLang)) {
        return targetLang;
      }
    }
  }

  // Method 2: Check if Google Translate has modified the page
  const gtComboElement = document.querySelector('.goog-te-combo');
  if (gtComboElement && gtComboElement instanceof HTMLSelectElement) {
    const selectedValue = gtComboElement.value;
    if (selectedValue && languages.some(lang => lang.code === selectedValue)) {
      return selectedValue;
    }
  }

  // Method 3: Check for Google Translate frame language
  try {
    const frames = window.frames;
    for (let i = 0; i < frames.length; i++) {
      try {
        const frame = frames[i];
        if (frame && frame.name && frame.name.includes('goog-te')) {
          // Google Translate frame detected, check its language
          const frameDoc = frame.document;
          if (frameDoc) {
            const langSelect = frameDoc.querySelector('select');
            if (langSelect && langSelect instanceof HTMLSelectElement) {
              const value = langSelect.value;
              if (value && languages.some(lang => lang.code === value)) {
                return value;
              }
            }
          }
        }
      } catch (_e) {
        // Cross-origin frame access denied, continue
      }
    }
  } catch (_e) {
    // Frame access error, continue
  }

  // Method 4: Check URL parameters (fallback)
  const urlParams = new URLSearchParams(window.location.search);
  const langParam = urlParams.get('lang');
  if (langParam && languages.some(lang => lang.code === langParam)) {
    return langParam;
  }

  // Method 5: Check localStorage for our own language preference
  try {
    const storedLang = localStorage.getItem('qualix-language');
    if (storedLang && languages.some(lang => lang.code === storedLang)) {
      return storedLang;
    }
  } catch (_e) {
    // localStorage access error
  }

  return 'pl'; // Default fallback
};

// Removed setCookie as we're using direct document.cookie manipulation for better control

interface LanguageSelectorProps {
  variant?: 'flags' | 'dropdown' | 'flags-mobile' | 'dropdown-mobile';
}

export default function LanguageSelector({ variant = 'flags' }: LanguageSelectorProps) {
  const [mounted, setMounted] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<string>('pl');

  useEffect(() => {
    setMounted(true);
    initializeGoogleTranslate();

    // Also detect current language on mount and after any changes
    const detectAndSetLanguage = () => {
      const currentLang = detectCurrentLanguage();
      setCurrentLanguage(currentLang);
    };

    detectAndSetLanguage();

    // Listen for storage changes (in case language is changed in another tab)
    const handleStorageChange = () => {
      detectAndSetLanguage();
    };

    window.addEventListener('storage', handleStorageChange);

    // Set up periodic language detection to catch Google Translate changes
    const intervalId = setInterval(() => {
      const detectedLang = detectCurrentLanguage();
      if (detectedLang !== currentLanguage) {
        setCurrentLanguage(detectedLang);
      }
    }, 1000); // Check every second

    // Set up mutation observer to detect Google Translate DOM changes
    const observer = new MutationObserver(() => {
      const detectedLang = detectCurrentLanguage();
      if (detectedLang !== currentLanguage) {
        setCurrentLanguage(detectedLang);
      }
    });

    // Observe changes to the document body and Google Translate elements
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'lang']
    });

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(intervalId);
      observer.disconnect();
    };
  }, [currentLanguage]);

  const initializeGoogleTranslate = () => {
    // Set up global configuration
    if (typeof window !== 'undefined') {
      window.__GOOGLE_TRANSLATION_CONFIG__ = {
        languages: languages,
        defaultLanguage: 'pl',
      };

      // Read current language from cookie with robust parsing
      const existingLanguageCookieValue = getCookie(COOKIE_NAME);
      let languageValue = 'pl'; // Default to Polish

      if (existingLanguageCookieValue) {
        // Handle different cookie formats: /auto/en, /pl/en, etc.
        const sp = existingLanguageCookieValue.split('/');
        if (sp.length >= 3 && sp[2]) {
          const detectedLang = sp[2];
          // Only use detected language if it's in our supported languages
          if (languages.some(lang => lang.code === detectedLang)) {
            languageValue = detectedLang;
          }
        }
      }

      setCurrentLanguage(languageValue);

      // Initialize Google Translate with error handling
      window.googleTranslateElementInit = () => {
        try {
          if (window.google && window.google.translate) {
            new window.google.translate.TranslateElement({
              pageLanguage: 'pl',
              includedLanguages: 'pl,en,de,uk',
              layout: 0, // Simple layout
              autoDisplay: false,
            });
            // Google Translate initialized successfully
          }
        } catch (error) {
          console.warn('Google Translate initialization failed:', error);
        }
      };

      // Load Google Translate script with error handling
      if (!document.getElementById('google-translate-script')) {
        const script = document.createElement('script');
        script.id = 'google-translate-script';
        script.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
        script.async = true;
        script.onerror = () => {
          console.warn('Failed to load Google Translate script');
        };
        document.body.appendChild(script);
      }
    }
  };

  const switchLanguage = (languageCode: string) => {
    // Update current language state immediately for visual feedback
    setCurrentLanguage(languageCode);

    // Store language preference in localStorage for better detection
    try {
      localStorage.setItem('qualix-language', languageCode);
    } catch (_e) {
      // localStorage not available
    }

    // NUCLEAR APPROACH: Clear ALL possible Google Translate cookies first
    const cookiesToClear = [
      'googtrans',
      'googtrans=/auto/pl',
      'googtrans=/auto/en',
      'googtrans=/auto/de',
      'googtrans=/auto/uk',
      'googtrans=/pl/en',
      'googtrans=/pl/de',
      'googtrans=/pl/uk',
      'googtrans=/en/pl',
      'googtrans=/de/pl',
      'googtrans=/uk/pl'
    ];

    // Clear all possible cookie variations
    cookiesToClear.forEach(cookieName => {
      document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; domain=.qualixsoftware.com`;
      document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; domain=qualixsoftware.com`;
      document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    });

    // Update current language state immediately for visual feedback
    setCurrentLanguage(languageCode);

    // Wait a moment for cookies to clear
    setTimeout(() => {
      if (languageCode === 'pl') {
        // For Polish, keep cookies cleared (original language)
        // Navigate to root URL without hash to prevent auto-scroll
        window.location.href = window.location.origin + window.location.pathname;
      } else {
        // For other languages, set the specific cookie format
        const cookieValue = `/auto/${languageCode}`;

        // Set cookie with multiple domain variations to ensure it works
        document.cookie = `googtrans=${cookieValue}; path=/; max-age=31536000; domain=.qualixsoftware.com`;
        document.cookie = `googtrans=${cookieValue}; path=/; max-age=31536000; domain=qualixsoftware.com`;
        document.cookie = `googtrans=${cookieValue}; path=/; max-age=31536000`;

        // Navigate to root URL without hash to prevent auto-scroll
        window.location.href = window.location.origin + window.location.pathname;
      }
    }, 100);
  };

  // Don't render anything if not mounted (prevents hydration issues)
  if (!mounted) {
    if (variant === 'dropdown') {
      return (
        <div className="w-24 h-10 rounded-lg theme-bg-secondary animate-pulse" />
      );
    }
    return (
      <div className="flex space-x-1">
        {languages.map((_, index) => (
          <div key={index} className="w-10 h-10 rounded-lg theme-bg-secondary animate-pulse" />
        ))}
      </div>
    );
  }

  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];

  // Don't render if no current language found
  if (!currentLang) return null;

  // Mobile dropdown variant (flags only, no country names)
  if (variant === 'dropdown-mobile') {
    return (
      <div className="language-selector">
        {/* Hidden Google Translate widget */}
        <div id="google_translate_element" style={{ display: 'none' }}></div>

        {/* Compact Dropdown Language Selector */}
        <Menu as="div" className="relative inline-block text-left">
          <MenuButton className="inline-flex items-center justify-center px-2 py-2 text-sm font-medium theme-text-secondary hover:text-primary-600 theme-bg-secondary hover:bg-primary-50 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            <span className="text-base notranslate" role="img" aria-label={`Flaga ${currentLang.name}`}>
              {currentLang.flag}
            </span>
            <ChevronDownIcon className="w-3 h-3 ml-1" aria-hidden="true" />
          </MenuButton>

          <MenuItems
            anchor="bottom start"
            className="w-24 mt-1 theme-bg-primary theme-border border rounded-lg shadow-lg focus:outline-none z-50"
          >
            <div className="py-1">
              {languages.map((language) => (
                <MenuItem key={language.code}>
                  {({ focus }) => (
                    <button
                      onClick={() => switchLanguage(language.code)}
                      className={`
                        ${focus ? 'bg-primary-50 text-primary-600' : 'theme-text-secondary'}
                        ${currentLanguage === language.code ? 'bg-primary-100 text-primary-700 font-medium' : ''}
                        group flex items-center justify-center w-full px-2 py-2 text-sm transition-colors duration-200
                      `}
                      aria-label={`Przełącz na język: ${language.name}`}
                    >
                      <span className="text-base notranslate" role="img" aria-label={`Flaga ${language.name}`}>
                        {language.flag}
                      </span>
                      {currentLanguage === language.code && (
                        <span className="ml-1 text-primary-600 text-xs">✓</span>
                      )}
                    </button>
                  )}
                </MenuItem>
              ))}
            </div>
          </MenuItems>
        </Menu>
      </div>
    );
  }

  if (variant === 'dropdown') {
    return (
      <div className="language-selector">
        {/* Hidden Google Translate widget */}
        <div id="google_translate_element" style={{ display: 'none' }}></div>

        {/* Dropdown Language Selector */}
        <Menu as="div" className="relative inline-block text-left">
          <MenuButton className="inline-flex items-center justify-center w-full px-3 py-2 text-sm font-medium theme-text-secondary hover:text-primary-600 theme-bg-secondary hover:bg-primary-50 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            <span className="mr-2 text-base notranslate" role="img" aria-label={`Flaga ${currentLang.name}`}>
              {currentLang.flag}
            </span>
            <span className="mr-1">{currentLang.name}</span>
            <ChevronDownIcon className="w-4 h-4 ml-1" aria-hidden="true" />
          </MenuButton>

          <MenuItems
            anchor="bottom start"
            className="w-40 mt-1 theme-bg-primary theme-border border rounded-lg shadow-lg focus:outline-none z-50"
          >
            <div className="py-1">
              {languages.map((language) => (
                <MenuItem key={language.code}>
                  {({ focus }) => (
                    <button
                      onClick={() => switchLanguage(language.code)}
                      className={`
                        ${focus ? 'bg-primary-50 text-primary-600' : 'theme-text-secondary'}
                        ${currentLanguage === language.code ? 'bg-primary-100 text-primary-700 font-medium' : ''}
                        group flex items-center w-full px-3 py-2 text-sm transition-colors duration-200
                      `}
                      aria-label={`Przełącz na język: ${language.name}`}
                    >
                      <span className="mr-3 text-base notranslate" role="img" aria-label={`Flaga ${language.name}`}>
                        {language.flag}
                      </span>
                      {language.name}
                      {currentLanguage === language.code && (
                        <span className="ml-auto text-primary-600">✓</span>
                      )}
                    </button>
                  )}
                </MenuItem>
              ))}
            </div>
          </MenuItems>
        </Menu>
      </div>
    );
  }

  // Mobile flags variant (compact, no tooltips)
  if (variant === 'flags-mobile') {
    return (
      <div className="language-selector">
        {/* Hidden Google Translate widget */}
        <div id="google_translate_element" style={{ display: 'none' }}></div>

        {/* Compact flag buttons for mobile */}
        <div className="flex space-x-1 notranslate" role="group" aria-label="Wybór języka">
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => switchLanguage(language.code)}
              className={`
                w-8 h-8 rounded-md theme-bg-secondary hover:opacity-80
                transition-all duration-200 flex items-center justify-center
                text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1
                ${currentLanguage === language.code ? 'ring-2 ring-primary-500' : ''}
              `}
              aria-label={`Przełącz na język: ${language.name}`}
              aria-pressed={currentLanguage === language.code}
              title={language.tooltip}
            >
              <span className="notranslate" role="img" aria-label={`Flaga ${language.name}`}>
                {language.flag}
              </span>
            </button>
          ))}
        </div>
      </div>
    );
  }

  // Original flags variant
  return (
    <div className="language-selector">
      {/* Hidden Google Translate widget */}
      <div id="google_translate_element" style={{ display: 'none' }}></div>

      {/* Custom flag buttons */}
      <div className="flex space-x-1 notranslate" role="group" aria-label="Wybór języka">
        {languages.map((language) => (
          <button
            key={language.code}
            onClick={() => switchLanguage(language.code)}
            className={`
              w-10 h-10 rounded-lg theme-bg-secondary hover:opacity-80
              transition-all duration-200 flex items-center justify-center
              text-lg hover:scale-105 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
              ${currentLanguage === language.code ? 'ring-2 ring-primary-500' : ''}
            `}
            data-tooltip-id={`language-tooltip-${language.code}`}
            data-tooltip-content={language.tooltip}
            aria-label={`Przełącz na język: ${language.name}`}
            aria-pressed={currentLanguage === language.code}
            title={language.tooltip}
          >
            <span className="notranslate" role="img" aria-label={`Flaga ${language.name}`}>
              {language.flag}
            </span>
          </button>
        ))}
      </div>

      {/* Tooltips */}
      {languages.map((language) => (
        <Tooltip
          key={`tooltip-${language.code}`}
          id={`language-tooltip-${language.code}`}
          place="bottom"
          className="z-50"
        />
      ))}
    </div>
  );
}
