'use client';

import { lazy, Suspense, useEffect } from 'react';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import FloatingButtons from '@/components/ui/FloatingButtons';
import CookiesBanner from '@/components/ui/CookiesBanner';
import Hero from '@/components/sections/Hero';
import About from '@/components/sections/About';
import Services from '@/components/sections/Services';
import Process from '@/components/sections/Process';
import WhyUs from '@/components/sections/WhyUs';
import TechStats from '@/components/sections/TechStats';
import Contact from '@/components/sections/Contact';
import { scrollToElement } from '@/lib/utils/scroll';


// Lazy load heavy sections for better initial page load performance
const FAQ = lazy(() => import('@/components/sections/FAQ'));
const Portfolio = lazy(() => import('@/components/sections/Portfolio'));
const Technologies = lazy(() => import('@/components/sections/Technologies'));

// Loading fallback component for lazy-loaded sections
const SectionSkeleton = () => (
  <div className="py-16 bg-white dark:bg-gray-900">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mx-auto mb-8"></div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[1, 2, 3].map((i) => (
            <div key={i} className="space-y-4">
              <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

export default function Home() {
  /*
    CRITICAL: Hash Navigation Handler for Blog-to-Homepage Navigation

    PROBLEM SOLVED: Blog CTA buttons (like "Bezpłatny audyt wydajności")
    use navigateToHomeSection('contact') which navigates to /#contact,
    but the homepage wasn't automatically scrolling to the target section.

    SOLUTION: This useEffect handles hash navigation by:
    1. Checking for hash in URL on page load
    2. Waiting 100ms for page to fully render
    3. Scrolling to the target section with proper offsets
    4. Handling hash changes for back/forward navigation

    USAGE: When users click blog CTAs, they navigate to /#contact and
    this code automatically scrolls to the contact form.
  */
  useEffect(() => {
    const handleHashNavigation = () => {
      const hash = window.location.hash;
      if (hash) {
        // Wait for page to fully load before scrolling
        setTimeout(() => {
          scrollToElement(hash, {
            behavior: 'smooth',
            mobileOffset: 20, // Slightly more offset for cross-page navigation
            desktopOffset: 80  // More offset needed for blog-to-homepage navigation
          });
        }, 100);
      }
    };

    // Handle initial load with hash
    handleHashNavigation();

    // Handle hash changes (back/forward navigation)
    window.addEventListener('hashchange', handleHashNavigation);

    return () => {
      window.removeEventListener('hashchange', handleHashNavigation);
    };
  }, []);

  return (
    <div className="min-h-screen">
      <Header />
      <main id="main-content">
        <Hero />
        <About />
        <Services />
        <Process />
        <WhyUs />
        <TechStats />
        <Suspense fallback={<SectionSkeleton />}>
          <FAQ />
        </Suspense>
        <Suspense fallback={<SectionSkeleton />}>
          <Portfolio />
        </Suspense>
        <Suspense fallback={<SectionSkeleton />}>
          <Technologies />
        </Suspense>
        <Contact />
      </main>
      <Footer />
      <FloatingButtons />
      <CookiesBanner />
    </div>
  );
}
