"use client";

import { Phone, MapPin, Clock, Mail, Instagram, MessageCircle, Star, Coffee, Croissant, Sandwich, Cake, Menu, X } from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";



// Menu items data
const menuItems = [
  {
    name: "Espresso Klasyczne",
    price: "8 zł",
    icon: Coffee,
    category: "coffee"
  },
  {
    name: "Cappuccino Brew Signature",
    price: "12 zł",
    icon: Coffee,
    category: "coffee"
  },
  {
    name: "Latte Wanili<PERSON>",
    price: "14 zł",
    icon: Coffee,
    category: "coffee"
  },
  {
    name: "Cold Brew Lodowy",
    price: "15 zł",
    icon: Coffee,
    category: "coffee"
  },
  {
    name: "Croissant Francuski",
    price: "9 zł",
    icon: Croissant,
    category: "food"
  },
  {
    name: "Sandwich Italiano",
    price: "18 zł",
    icon: Sandwich,
    category: "food"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    price: "12 zł",
    icon: Cake,
    category: "food"
  },
  {
    name: "Bagietka z Awokado",
    price: "16 zł",
    icon: Sandwich,
    category: "food"
  }
];

// Instagram feed mockup data
const instagramPosts = [
  { id: 1, alt: "Świeżo parzona kawa" },
  { id: 2, alt: "Croissant z dżemem" },
  { id: 3, alt: "Latte art" },
  { id: 4, alt: "Wnętrze kawiarni" },
  { id: 5, alt: "Sandwich z awokado" },
  { id: 6, alt: "Ciasto czekoladowe" },
  { id: 7, alt: "Cappuccino" },
  { id: 8, alt: "Zespół kawiarni" }
];

export default function BrewAndBeanDemo() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80; // Account for fixed header
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
    setMobileMenuOpen(false); // Close mobile menu after clicking
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (mobileMenuOpen && !target.closest('.mobile-menu') && !target.closest('.mobile-menu-button')) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [mobileMenuOpen]);

  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 theme-bg-primary/90 backdrop-blur-md border-b theme-border shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Coffee className="h-8 w-8 text-amber-600 drop-shadow-sm" />
              <span className="text-xl font-bold theme-text-primary drop-shadow-sm">Brew & Bean</span>
            </div>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-8">
              <button onClick={() => scrollToSection('menu')} className="theme-text-tertiary hover:theme-text-primary transition-colors font-medium drop-shadow-sm">Menu</button>
              <button onClick={() => scrollToSection('about')} className="theme-text-tertiary hover:theme-text-primary transition-colors font-medium drop-shadow-sm">O nas</button>
              <button onClick={() => scrollToSection('location')} className="theme-text-tertiary hover:theme-text-primary transition-colors font-medium drop-shadow-sm">Lokalizacja</button>
              <button onClick={() => scrollToSection('contact')} className="theme-text-tertiary hover:theme-text-primary transition-colors font-medium drop-shadow-sm">Kontakt</button>
              <a href="tel:+48555123456" className="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-lg transition-colors font-semibold shadow-lg">
                Zadzwoń
              </a>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="mobile-menu-button theme-text-primary hover:theme-text-secondary transition-colors p-2 drop-shadow-sm"
                aria-label="Toggle menu"
              >
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="mobile-menu md:hidden absolute top-16 left-0 right-0 theme-bg-primary/95 backdrop-blur-md border-b theme-border shadow-lg">
              <div className="px-4 py-4 space-y-4">
                <button onClick={() => scrollToSection('menu')} className="block w-full text-left theme-text-tertiary hover:theme-text-primary transition-colors py-2 font-medium">Menu</button>
                <button onClick={() => scrollToSection('about')} className="block w-full text-left theme-text-tertiary hover:theme-text-primary transition-colors py-2 font-medium">O nas</button>
                <button onClick={() => scrollToSection('location')} className="block w-full text-left theme-text-tertiary hover:theme-text-primary transition-colors py-2 font-medium">Lokalizacja</button>
                <button onClick={() => scrollToSection('contact')} className="block w-full text-left theme-text-tertiary hover:theme-text-primary transition-colors py-2 font-medium">Kontakt</button>
                <a href="tel:+48555123456" className="block bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-lg transition-colors text-center font-semibold shadow-lg">
                  Zadzwoń: +48 555 123 456
                </a>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-900/80 to-amber-800/60 z-10"></div>
        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')] bg-cover bg-center"></div>
        
        <div className="relative z-20 text-center text-white max-w-4xl mx-auto px-4">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Najlepsza kawa w sercu Warszawy
          </h1>
          <p className="text-xl md:text-2xl mb-8 opacity-90">
            Każdego ranka świeżo palona • Lokalnie palona • Rodzinna tradycja od 2018
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => scrollToSection('menu')}
              className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105"
            >
              Zobacz Menu
            </button>
            <a
              href="tel:+48555123456"
              className="border-2 border-white text-white hover:bg-white hover:text-amber-900 px-8 py-4 rounded-lg text-lg font-semibold transition-all"
            >
              Zadzwoń: +48 555 123 456
            </a>
          </div>
        </div>
      </section>

      {/* Menu Highlights Section */}
      <section id="menu" className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Nasze Specjalności
            </h2>
            <div className="w-24 h-1 bg-amber-600 mx-auto mb-6"></div>
            <p className="text-lg theme-text-secondary max-w-2xl mx-auto">
              Odkryj nasze najlepsze napoje i przekąski, przygotowane z najwyższą starannością
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {menuItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <div 
                  key={index}
                  className="theme-bg-card rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
                >
                  <div className="flex items-center justify-between mb-4">
                    <Icon className={`h-8 w-8 ${item.category === 'coffee' ? 'text-amber-600' : 'text-orange-500'} group-hover:scale-110 transition-transform`} />
                    <span className="text-2xl font-bold text-amber-600">{item.price}</span>
                  </div>
                  <h3 className="text-lg font-semibold theme-text-primary mb-2">{item.name}</h3>
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>
              );
            })}
          </div>

          <div className="text-center mt-12">
            <div className="theme-bg-card border-2 border-amber-200 dark:border-amber-700 rounded-xl p-6 max-w-md mx-auto shadow-lg">
              <h3 className="text-lg font-semibold theme-text-primary mb-2">Zamówienia telefoniczne</h3>
              <p className="theme-text-secondary mb-4">Przyjmujemy zamówienia przez telefon i WhatsApp</p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <a href="tel:+48555123456" className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-lg transition-colors font-semibold flex items-center justify-center space-x-2 shadow-md">
                  <Phone className="h-5 w-5" />
                  <span>Zadzwoń</span>
                </a>
                <a href="https://wa.me/48555123456" className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors font-semibold flex items-center justify-center space-x-2 shadow-md">
                  <MessageCircle className="h-5 w-5" />
                  <span>WhatsApp</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-6">
                Nasza Historia
              </h2>
              <p className="text-lg theme-text-secondary mb-6 leading-relaxed">
                Brew & Bean to rodzinna kawiarnia w centrum Warszawy, którą założyliśmy z pasji do wyjątkowej kawy. 
                Współpracujemy z lokalnymi palarniami i piekarniami, tworząc autentyczne doświadczenie dla każdego gościa.
              </p>
              <p className="text-lg theme-text-secondary mb-8 leading-relaxed">
                Nasze ziarna pochodzą z certyfikowanych farm, a każda filiżanka jest przygotowywana z dbałością o najwyższą jakość.
              </p>
              
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center p-4 theme-bg-secondary rounded-lg">
                  <Coffee className="h-8 w-8 text-amber-600 mx-auto mb-2" />
                  <h4 className="font-semibold theme-text-primary">Lokalne produkty</h4>
                </div>
                <div className="text-center p-4 theme-bg-secondary rounded-lg">
                  <Star className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h4 className="font-semibold theme-text-primary">Zrównoważony rozwój</h4>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="aspect-square rounded-2xl overflow-hidden shadow-2xl">
                <div className="w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center">
                  <Coffee className="h-32 w-32 text-amber-600" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Location & Hours */}
      <section id="location" className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Odwiedź Nas
            </h2>
            <div className="w-24 h-1 bg-amber-600 mx-auto"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Map placeholder */}
            <div className="aspect-video bg-gray-200 rounded-xl overflow-hidden shadow-lg">
              <div className="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                <MapPin className="h-16 w-16 text-gray-600" />
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-6">
              <div className="theme-bg-card p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-4 mb-4">
                  <MapPin className="h-6 w-6 text-amber-600" />
                  <div>
                    <h3 className="font-semibold theme-text-primary">Adres</h3>
                    <p className="theme-text-secondary">ul. Chmielna 15, 00-021 Warszawa</p>
                  </div>
                </div>
              </div>

              <div className="theme-bg-card p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-4 mb-4">
                  <Clock className="h-6 w-6 text-amber-600" />
                  <div>
                    <h3 className="font-semibold theme-text-primary">Godziny otwarcia</h3>
                    <div className="theme-text-secondary">
                      <p>Poniedziałek - Piątek: 7:00 - 18:00</p>
                      <p>Sobota - Niedziela: 8:00 - 20:00</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="theme-bg-card p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-4 mb-4">
                  <Phone className="h-6 w-6 text-amber-600" />
                  <div>
                    <h3 className="font-semibold theme-text-primary">Kontakt</h3>
                    <p className="theme-text-secondary">+48 555 123 456</p>
                    <p className="theme-text-secondary"><EMAIL></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Instagram Feed Mockup */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Śledź nas na @brewandbean
            </h2>
            <div className="w-24 h-1 bg-amber-600 mx-auto mb-6"></div>
            <p className="text-lg theme-text-secondary">
              Zobacz nasze najnowsze zdjęcia i bądź na bieżąco z naszymi nowościami
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
            {instagramPosts.map((post) => (
              <div
                key={post.id}
                className="aspect-square bg-gradient-to-br from-amber-100 to-amber-200 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer group"
              >
                <div className="w-full h-full flex items-center justify-center group-hover:bg-amber-300/50 transition-colors">
                  <Instagram className="h-8 w-8 text-amber-600 group-hover:scale-110 transition-transform" />
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <a
              href="https://instagram.com"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105"
            >
              <Instagram className="h-6 w-6" />
              <span>Obserwuj nas</span>
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Skontaktuj się z nami
            </h2>
            <div className="w-24 h-1 bg-amber-600 mx-auto mb-6"></div>
            <p className="text-lg theme-text-secondary">
              Masz pytania? Chcesz zarezerwować stolik? Napisz do nas!
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="theme-bg-card p-8 rounded-xl shadow-lg">
              <form className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium theme-text-primary mb-2">
                    Imię i nazwisko *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="w-full px-4 py-3 border theme-border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent theme-bg-primary theme-text-primary"
                    placeholder="Twoje imię i nazwisko"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium theme-text-primary mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="w-full px-4 py-3 border theme-border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent theme-bg-primary theme-text-primary"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium theme-text-primary mb-2">
                    Telefon
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    className="w-full px-4 py-3 border theme-border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent theme-bg-primary theme-text-primary"
                    placeholder="+48 123 456 789"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium theme-text-primary mb-2">
                    Wiadomość *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={4}
                    required
                    className="w-full px-4 py-3 border theme-border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent theme-bg-primary theme-text-primary"
                    placeholder="Twoja wiadomość..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-amber-600 hover:bg-amber-700 text-white py-4 px-6 rounded-lg text-lg font-semibold transition-all transform hover:scale-105"
                >
                  Wyślij wiadomość
                </button>
              </form>
            </div>

            {/* Contact Details */}
            <div className="space-y-6">
              <div className="theme-bg-card p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-4">
                  <div className="bg-amber-100 p-3 rounded-full">
                    <Phone className="h-6 w-6 text-amber-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold theme-text-primary">Telefon</h3>
                    <a href="tel:+48555123456" className="text-amber-600 hover:text-amber-700 transition-colors">
                      +48 555 123 456
                    </a>
                  </div>
                </div>
              </div>

              <div className="theme-bg-card p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-4">
                  <div className="bg-green-100 p-3 rounded-full">
                    <MessageCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold theme-text-primary">WhatsApp</h3>
                    <a href="https://wa.me/48555123456" className="text-green-600 hover:text-green-700 transition-colors">
                      Napisz na WhatsApp
                    </a>
                  </div>
                </div>
              </div>

              <div className="theme-bg-card p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-4">
                  <div className="bg-blue-100 p-3 rounded-full">
                    <Mail className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold theme-text-primary">Email</h3>
                    <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700 transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>

              <div className="theme-bg-card p-6 rounded-xl shadow-lg">
                <h3 className="font-semibold theme-text-primary mb-4">Dostawa</h3>
                <p className="theme-text-secondary mb-2">🚚 Dostawa w centrum Warszawy w 30 min</p>
                <p className="theme-text-secondary">💡 Zamówienia przyjmujemy telefonicznie</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="theme-bg-primary border-t theme-border py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Left column */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Coffee className="h-8 w-8 text-amber-600" />
                <span className="text-xl font-bold theme-text-primary">Brew & Bean</span>
              </div>
              <p className="theme-text-secondary mb-6">
                Najlepsza kawa w sercu Warszawy
              </p>
              <div className="flex space-x-4">
                <a href="https://instagram.com" className="text-pink-500 hover:text-pink-600 transition-colors">
                  <Instagram className="h-6 w-6" />
                </a>
                <a href="https://facebook.com" className="text-blue-500 hover:text-blue-600 transition-colors">
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
              </div>
            </div>

            {/* Center column */}
            <div>
              <h3 className="font-semibold theme-text-primary mb-4">Szybkie linki</h3>
              <ul className="space-y-2">
                <li><button onClick={() => scrollToSection('menu')} className="theme-text-secondary hover:theme-text-primary transition-colors">Menu</button></li>
                <li><button onClick={() => scrollToSection('about')} className="theme-text-secondary hover:theme-text-primary transition-colors">O nas</button></li>
                <li><button onClick={() => scrollToSection('location')} className="theme-text-secondary hover:theme-text-primary transition-colors">Lokalizacja</button></li>
                <li><button onClick={() => scrollToSection('contact')} className="theme-text-secondary hover:theme-text-primary transition-colors">Kontakt</button></li>
              </ul>
              <div className="mt-6">
                <h4 className="font-semibold theme-text-primary mb-2">Godziny otwarcia</h4>
                <p className="theme-text-secondary text-sm">Pn-Pt: 7:00-18:00</p>
                <p className="theme-text-secondary text-sm">Sb-Nd: 8:00-20:00</p>
              </div>
            </div>

            {/* Right column */}
            <div>
              <h3 className="font-semibold theme-text-primary mb-4">Kontakt</h3>
              <div className="space-y-2 theme-text-secondary">
                <p>ul. Chmielna 15</p>
                <p>00-021 Warszawa</p>
                <p>+48 555 123 456</p>
              </div>
              <div className="mt-6">
                <h4 className="font-semibold theme-text-primary mb-2">Newsletter</h4>
                <div className="flex">
                  <input
                    type="email"
                    placeholder="Twój email"
                    className="flex-1 px-3 py-2 border theme-border rounded-l-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent theme-bg-primary theme-text-primary text-sm"
                  />
                  <button className="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-r-lg transition-colors text-sm">
                    Zapisz
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t theme-border mt-12 pt-8 text-center theme-text-secondary">
            <p>© 2025 Brew & Bean. Demo projekt stworzony przez <Link href="/" className="text-amber-600 hover:text-amber-700 transition-colors">Qualix Software</Link> | Wszystkie zdjęcia: Unsplash/Pexels</p>
          </div>
        </div>
      </footer>



      {/* Mobile Menu Button (for future enhancement) */}
      <div className="md:hidden fixed bottom-4 right-4 z-40">
        <button className="bg-amber-600 hover:bg-amber-700 text-white p-3 rounded-full shadow-lg transition-all">
          <Phone className="h-6 w-6" />
        </button>
      </div>
    </div>
  );
}
